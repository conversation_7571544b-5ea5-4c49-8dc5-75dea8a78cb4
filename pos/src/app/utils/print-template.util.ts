import { PrintTemplateData } from '../models/print.model';
import { DEFAULT_STORE_CONFIG } from '../constants/store.constants';

export class PrintTemplateUtil {

  /**
   * Generate the complete HTML template for printing
   **/
  static generateTemplate(data: PrintTemplateData): string {
    const header = this.generateHeader(data);
    const orderInfo = this.generateOrderInfo(data);
    const itemsTable = this.generateItemsTable(data.items, data.totals);
    const totals = this.generateTotals(data, data.totals);
    const footer = this.generateFooter(data.payment_method || 'Cash', data);
    
    // Add COPY watermark if this is a duplicate print
    const copyWatermark = data.copy_of_invoice 
      ? `
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
                    text-4xl font-bold text-red-200 opacity-30 -rotate-45 pointer-events-none">
          INVOICE COPY 
        </div>
      ` 
      : '';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>ROZANA - billing receipt ${data.order_id}${data.copy_of_invoice ? '(COPY)' : ''}</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          @media print {
            @page {
              size: 80mm auto;
              margin: 0;
            }
            body {
              padding: 5mm;
              position: relative;
            }
          }
        </style>
      </head>
      <body class="font-mono text-xs text-black bg-white p-2 max-w-[300px] mx-auto relative">
        ${copyWatermark}
        <div class="relative z-10">
          ${header}
          ${orderInfo}
          ${itemsTable}
          ${totals}
          ${footer}
        </div>
      </body>
      </html>
    `;
  }

  private static generateHeader(data: PrintTemplateData): string {
    return `
      <div class="text-center">
        <div class="text-sm font-bold mb-0.5">${data.facility_name}</div>
        <div class="text-[10px] mb-0.5">${data.storeAddress}</div>
        <div class="text-[10px] mb-0.25">Ph: ${data.storePhone}</div>
        <div class="text-[10px] mb-0.25">Email: ${DEFAULT_STORE_CONFIG.EMAIL}</div>
        <div class="text-[10px] mb-0.25">GSTIN: ${data.storeGSTIN}</div>
        <div class="text-[12px] font-bold text-center underline mb-1 pb-1">TAX INVOICE</div>
      </div>
    `;
  }

  private static generateOrderInfo(data: PrintTemplateData): string {
    return `
      <div class="mb-2 border-b  border-t border-dashed border-black p-2">
        <div class="flex justify-between">
          <span>Order ID:</span>
          <span>${data.order_id}</span>
        </div>
        <div class="flex justify-between">
          <span>Date:</span>
          <span>${data.currentDate}</span>
        </div>
        ${data.customer_name ? `
          <div class="flex justify-between">
            <span>Customer:</span>
            <span>${data.customer_name}</span>
          </div>
        ` : ''}
      </div>
    `;
  }

  private static generateItemsTable(items: any[], totals: any): string {
    const hasIGST = totals.totalIgstAmount > 0;
    const hasCGST_SGST = totals.totalCgstAmount > 0 || totals.totalSgstAmount > 0;

    return `
      <table class="w-full mb-2">
        <p class="text-center font-bold mb-2">ITEMS PURCHASED</p>
        <thead>
          <tr class="border-t border-b border-black text-xs">
            <th class="text-left py-1 text-xs">Item Details</th>
            <th class="py-1 text-xs">Qty</th>
            <th class="py-1 text-xs">MRP</th>
            <th class="py-1 text-xs">Price</th>
            <th class="py-1 text-xs">Total</th>
          </tr>
        </thead>
        <tbody>
        ${items.map((item, index) => {
          // Format the tax display string showing rates and amounts
          let taxDisplay = '';

          if (hasIGST && item.igst > 0) {
            // Show IGST rate and amount, plus split CGST/SGST rates
            const cgstRate = item.igst / 2;
            const sgstRate = item.igst / 2;
            taxDisplay = `IGST: ${item.igst}% (${this.formatCurrency(item.igstAmount || 0)}) ` +
                        `[CGST: ${cgstRate}% (${this.formatCurrency(item.cgstAmount || 0)}), ` +
                        `SGST: ${sgstRate}% (${this.formatCurrency(item.sgstAmount || 0)})]`;
          } else {
            taxDisplay = 'No GST';
          }

          // Add CESS rate and amount if present
          if (item.cess && item.cess > 0) {
            taxDisplay += `, CESS: ${item.cess}% (${this.formatCurrency(item.cessAmount || 0)})`;
          }

          // Show total tax amount
          if (item.totalTax && item.totalTax > 0) {
            taxDisplay += ` | Total Tax: ${this.formatCurrency(item.totalTax)}`;
          }

          return `
            <tr>
              <td class="px-2 py-1 text-xs">${index + 1}. ${item.name || item.sku}</td>
              <td class="py-1 text-center text-xs">${item.quantity}</td>
              <td class="py-1 text-right text-xs">${this.formatCurrency(item.mrp || item.unit_price)}</td>
              <td class="py-1 text-right text-xs">${this.formatCurrency(item.sale_price)}</td>
              <td class="py-1 text-right text-xs">${this.formatCurrency(item.totalWithTax || (item.quantity * item.sale_price))}</td>
            </tr>
            <tr class="border-b border-dashed border-gray-200">
              <td colspan="5" class="px-2 py-1">
                <div class="text-xs">
                  <div class="font-medium">
                    <span class="text-gray-600 text-xs">${taxDisplay}</span>
                  </div>
                </div>
              </td>
            </tr>
          `;
        }).join('')}
        </tbody>
      </table>
    `;
  }


  private static generateTotals(orderData: any, totals: any): string {
    const hasGST = totals.totalGstAmount > 0;
    const hasCGST_SGST = totals.totalCgstAmount > 0 || totals.totalSgstAmount > 0;
    const hasCESS = totals.totalCessAmount > 0;
    const grandTotal = totals.totalTaxableAmount + totals.totalGstAmount + totals.totalCessAmount;

    return `
      <div class="border-t border-dashed border-black pt-1">
        <div class="text-center border-b border-dashed border-black font-bold mb-2">
          <div class="flex justify-between">
            <span>Total Items: ${orderData.items.length}</span>
            <span>Total Qty: ${totals.totalQuantity}</span>
          </div>
        </div>
        <div class="space-y-1">
          <div class="flex justify-between">
            <span>Subtotal:</span>
            <span>${this.formatCurrency(totals.totalTaxableAmount)}</span>
          </div>
          ${hasCGST_SGST ? `
            <div class="flex justify-between">
              <span>Total CGST:</span>
              <span>${this.formatCurrency(totals.totalCgstAmount)}</span>
            </div>
            <div class="flex justify-between">
              <span>Total SGST:</span>
              <span>${this.formatCurrency(totals.totalSgstAmount)}</span>
            </div>
          ` : ''}

          ${hasCESS ? `
            <div class="flex justify-between">
              <span>Total CESS:</span>
              <span>${this.formatCurrency(totals.totalCessAmount)}</span>
            </div>

          ` : ''}
          ${hasGST ? `
            <div class="flex justify-between">
              <span>Total GST:</span>
              <span>${this.formatCurrency(totals.totalGstAmount)}</span>
            </div>
          ` : ''}
          <div class="flex justify-between font-bold border-t border-black pt-1 mt-1">
            <span>Grand Total:</span>
            <span>${this.formatCurrency(grandTotal)}</span>
          </div>
        </div>
      </div>
    `;
  }

  private static generateFooter(paymentMethod: string = 'Cash', orderData: any): string {
    return `
      <div class="mt-4 text-center text-[10px]">
        <div class="flex justify-center font-bold">
          <span>Payment Mode:&nbsp;</span>
          <span>${paymentMethod}</span>
        </div>
        ${orderData.discount > 0 ? `
          <div class="flex justify-center border border-black pt-1 font-bold mt-1">
            <span>You saved:&nbsp;</span>
            <span>${this.formatCurrency(orderData.discount || 0)}</span>
          </div>
        ` : ''}
        <div class="mt-2 border-t border-dashed border-black pt-1">ROZANA - Rural Commerce Pvt Ltd</div>
        <div class="mt-1 font-bold">Thank you for shopping !</div>
        <div class="mt-1">Visit Again</div>
        <div class="mt-1">This is a computer generated receipt</div>

      </div>
    `;
  }

  private static formatCurrency(amount: number): string {
    return '₹' + amount.toFixed(2);
  }
}
