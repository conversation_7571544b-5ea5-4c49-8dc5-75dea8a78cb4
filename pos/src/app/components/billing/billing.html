<div class="w-full bg-white p-3 rounded-lg  max-h-screen overflow-y-auto">
  <div class="w-full flex items-center justify-between">
    <h6>Billing Section</h6>
    <button *ngIf="showActions" pButton icon="pi pi-times" iconPos="right" severity="danger" size="small"
      [outlined]="true" [label]="'Clear Cart'" class="hidden sm:inline-flex" (click)="clearCart()"></button>
  </div>
  <div class="w-full mt-3">
    <div class="w-full flex flex-col items-center justify-center py-8" *ngIf="cartItems.length == 0">
      <app-no-data [description]="noDataMessage" [title]="noDataTitle" [image]="'https://img.freepik.com/premium-vector/payment-schedule-email-notification-computer_197170-647.jpg'"></app-no-data>
    </div>
    <p-divider *ngIf="cartItems.length > 0" />
    <div class="w-full mt-3" *ngIf="cartItems.length > 0" >
      <div class="w-full" *ngIf="showTable" ngClass="mb-3">
        <app-table [pagination]="false" [tableData]="cartItems" [tableColumns]="cartColumns"></app-table>
      </div>
      <p class="w-full flex justify-between mb-2 mt-0 text-xs sm:text-sm">
        <span>Total Items:</span>
        <span class="font-medium">{{cartItems.length}}</span>
      </p>
      <p class="w-full flex justify-between mb-2 text-xs sm:text-sm">
        <span>Sub Total:</span>
        <span class="font-medium">₹{{getSubTotal().toFixed(2)}}</span>
      </p>
      <p class="w-full flex justify-between mb-2 text-xs sm:text-sm" *ngIf="getDiscount() > 0">
        <span>Discount:</span>
        <span class="font-medium text-green-600">-₹{{getDiscount().toFixed(2)}}</span>
      </p>
      <p class="w-full flex justify-between mb-2 text-xs sm:text-sm">
        <span>Taxes (GST + CESS):</span>
        <span class="font-medium">₹{{getTaxes().toFixed(2)}}</span>
      </p>
      <p class="w-full flex justify-between mb-2 font-bold text-sm sm:text-base">
        <span>Grand Total:</span>
        <span>₹{{getGrandTotal().toFixed(2)}}</span>
      </p>
    </div>

    <div class="w-full flex flex-col gap-2 mt-3" *ngIf="showActions && cartItems.length > 0 && !isProcessingCheckout">
      <button class="w-full" pButton severity="info" [outlined]="true" label="Confirm Order" (click)="createOrder()"></button>
    </div>

    <div class="w-full flex items-center justify-center py-4" *ngIf="isProcessingCheckout && showActions">
      <p-progressSpinner [style]="{'width': '30px', 'height': '30px'}" strokeWidth="4" animationDuration="1s">
      </p-progressSpinner>
      <span class="ml-3 text-sm text-gray-600">Processing order...</span>
    </div>
  </div>
</div>