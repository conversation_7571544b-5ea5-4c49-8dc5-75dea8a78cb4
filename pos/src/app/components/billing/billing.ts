import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges, ChangeDetectorRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { InputNumberModule } from 'primeng/inputnumber';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { CommonService } from '../../services/common';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { NoDataComponent } from "../no-data/no-data";
import { StorageService } from '../../services/storage';
import { OrderService } from '../../services/order.service';
import { PrintService } from '../../services/print.service';
import { CartItem, OrderItem } from '../../models';
import { TableComponent } from "../table/table";
import { CartCalculationUtils } from '../../utils/cart-calculation.utils';
@Component({
  selector: 'app-billing',
  standalone: true,
  templateUrl: './billing.html',
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    InputNumberModule,
    DividerModule,
    ButtonModule,
    ProgressSpinnerModule,
    NoDataComponent,
    TableComponent
],
})
export class BillingComponent implements OnChanges {
  showConfirmDialog = false;
  @Input() showTable = false;
  @Input() cartItems: CartItem[] = [];
  @Input() quantityEdit = false;
  @Input() showActions = false;
  @Output() cartChange = new EventEmitter<CartItem[]>();
  @Input() cartColumns = [
    { field: 'thumbnail_image', header: 'Image', type: 'image' },
    { field: 'name', header: 'SKU' },
    { field: 'selling_price', header: 'Price' },
    { field: 'quantity', header: 'Quantity'}
  ]
  @Input() noDataTitle = 'Add items to cart';
  @Input() noDataMessage = '';
  isProcessingCheckout = false;

  constructor(
    private commonService: CommonService,
    private cdr: ChangeDetectorRef,
    private storageService: StorageService,
    private orderService: OrderService,
    private printService: PrintService
  ) { }

  ngOnChanges(_changes: SimpleChanges): void {
    console.log(this.cartItems);
  }

  removeFromCart(product: CartItem) {
    const index = this.cartItems.findIndex((item) => item.id === product.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
      this.cartChange.emit([...this.cartItems]);
    }
  }

  updateQuantity(event: { value: number }, product: CartItem) {
    const newQuantity = event.value;
    if (newQuantity < 1) {
      this.removeFromCart(product);
    } else {
      product.quantity = newQuantity;
      this.cartChange.emit([...this.cartItems]);
    }
  }

  getSubTotal() {
    return CartCalculationUtils.calculateSubTotal(this.cartItems);
  }

  getDiscount() {
    return CartCalculationUtils.calculateDiscount(this.cartItems);
  }

  getTaxes() {
    return this.cartItems.reduce((total, item) => {
      const itemTaxableAmount = item.selling_price * item.quantity;
      let itemTotalTax = 0;

      // Calculate IGST
      if (item.igst && item.igst > 0) {
        itemTotalTax += (itemTaxableAmount * item.igst) / 100;
      }

      // Add CESS
      if (item.cess && item.cess > 0) {
        itemTotalTax += (itemTaxableAmount * item.cess) / 100;
      }

      return total + itemTotalTax;
    }, 0);
  }

  getGrandTotal() {
    return this.getSubTotal() - this.getDiscount() + this.getTaxes();
  }

  clearCart() {
    this.cartItems.length = 0;
    this.cartChange.emit([...this.cartItems]);
    this.cdr.detectChanges();
  }

  confirmCheckout() {
    this.showConfirmDialog = true;
  }

  onPaymentCancel() {
    this.showConfirmDialog = false;
  }

  createOrder() {
    try {
      const data = {
        total: this.getGrandTotal(),
        items: this.cartItems.map((item): Omit<OrderItem, 'total_price'> => ({
          sku: item.child_sku,
          unit_price: item.selling_price,
          sale_price: item.selling_price,
          quantity: item.quantity,
          tax: item.tax,
        })),
      }
      this.isProcessingCheckout = true;
      this.orderService.createOrder(data).then((order: any) => {
        this.commonService.toast({ severity: 'success', summary: 'Success', detail: `Order created successfully! Total: ₹${this.getGrandTotal().toFixed(2)}` });

        // Print tax receipt after successful order creation
        if (order?.data?.order_id) {
          this.printReceipt(order.data.order_id);
        }

        this.clearCart();
        this.showConfirmDialog = false;
        this.isProcessingCheckout = false;
      }).catch((error: any) => {
        let errorMessage = 'Order creation failed. Please try again.';
        if (error.status === 401) {
          errorMessage = 'Authentication failed. Please check your token.';
        } else if (error.status === 400) {
          errorMessage = 'Invalid order data. Please check the items.';
        }
        this.commonService.toast({ severity: 'error', summary: 'Error', detail: errorMessage });
        this.isProcessingCheckout = false;
      })
    } catch (error) {
      this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to create order. Please try again.' });
      this.isProcessingCheckout = false;
    }
  }
  // Print tax receipt for cart items
  printReceipt(orderId?: string): void {
    if (this.cartItems.length === 0) {
      this.commonService.toast({ severity: 'warn', summary: 'Warning', detail: 'No items in cart to print' });
      return;
    }
    const user: any = this.storageService.getItem('user');
    const customerName = user?.displayName || 'Walk-in Customer';
    const orderIdToPrint = orderId || `TEMP-${Date.now()}`;

    this.printService.printCart(
      this.cartItems,
      orderIdToPrint,
      customerName,
      'Cash',
      false
    );
  }
}
